# 资产盘点功能开发任务文档

## 项目概述

### 项目背景
基于现有的RuoYi框架资产管理系统，新增资产盘点功能模块，实现资产的定期盘点、差异分析、结果处理等完整业务流程。

### 项目目标
- 建立完整的资产盘点管理体系
- 提高资产管理的准确性和及时性
- 减少人工盘点的工作量和错误率
- 为资产管理决策提供数据支持

### 技术架构
- 后端：基于RuoYi框架，使用Spring Boot + MyBatis Plus
- 前端：Vue.js + Element UI
- 数据库：MySQL
- 移动端：考虑后期扩展微信小程序或移动APP

## 功能模块划分

### 1. 盘点计划管理模块
**负责人：** 待分配  
**预估工期：** 5个工作日  
**优先级：** 高

#### 功能清单
- [ ] 盘点计划创建
- [ ] 盘点范围设置（按部门、类别、位置筛选）
- [ ] 盘点人员分配
- [ ] 盘点计划审批流程
- [ ] 盘点计划查询和管理

#### 技术要求
- 创建InventoryPlanController控制器
- 实现InventoryPlanService业务逻辑
- 设计inventory_plan数据表
- 集成RuoYi权限控制体系

### 2. 盘点任务执行模块
**负责人：** 待分配  
**预估工期：** 7个工作日  
**优先级：** 高

#### 功能清单
- [ ] 盘点任务自动分发
- [ ] 盘点任务领取和执行
- [ ] 资产扫码盘点功能
- [ ] 手动录入盘点结果
- [ ] 盘点进度实时跟踪
- [ ] 盘点任务状态管理

#### 技术要求
- 创建InventoryTaskController控制器
- 实现任务分发算法
- 设计inventory_task和inventory_record数据表
- 前端集成扫码组件

### 3. 盘点差异分析模块
**负责人：** 待分配  
**预估工期：** 4个工作日  
**优先级：** 中

#### 功能清单
- [ ] 盘点结果与账面数据对比
- [ ] 盘盈盘亏资产识别
- [ ] 资产状态差异分析
- [ ] 资产位置差异分析
- [ ] 差异原因登记
- [ ] 差异处理建议

#### 技术要求
- 创建InventoryDifferenceController控制器
- 实现差异分析算法
- 设计inventory_difference数据表
- 提供差异数据统计接口

### 4. 盘点报告生成模块
**负责人：** 待分配  
**预估工期：** 3个工作日  
**优先级：** 中

#### 功能清单
- [ ] 盘点汇总报告生成
- [ ] 差异明细报告导出
- [ ] 部门盘点报告统计
- [ ] 资产变动报告
- [ ] 报告模板自定义
- [ ] 报告数据可视化

#### 技术要求
- 创建InventoryReportController控制器
- 集成RuoYi Excel导出功能
- 设计inventory_report数据表
- 前端图表展示组件

### 5. 盘点后处理模块
**负责人：** 待分配  
**预估工期：** 4个工作日  
**优先级：** 中

#### 功能清单
- [ ] 资产台账信息更新
- [ ] 盘盈资产入账处理
- [ ] 盘亏资产核销流程
- [ ] 与资产处置模块集成
- [ ] 盘点结果确认审批
- [ ] 责任追究记录

#### 技术要求
- 与现有AssetLedgerController集成
- 与AssetDisposalController集成
- 实现数据同步更新机制
- 设计审批工作流

## 数据库设计

### 核心数据表

#### 1. 盘点计划表 (asset_stocktaking_plan)
```sql
CREATE TABLE asset_stocktaking_plan (
    plan_id VARCHAR(32) PRIMARY KEY COMMENT '盘点计划ID',
    plan_name VARCHAR(100) NOT NULL COMMENT '盘点计划名称',
    plan_type TINYINT DEFAULT 1 COMMENT '盘点类型：1-全盘，2-部分盘点',
    plan_scope TEXT COMMENT '盘点范围（JSON格式）',
    start_date DATE COMMENT '计划开始日期',
    end_date DATE COMMENT '计划结束日期',
    responsible_user_id BIGINT COMMENT '负责人ID',
    status TINYINT DEFAULT 1 COMMENT '状态：1-草稿，2-待审批，3-执行中，4-已完成，5-已取消',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark TEXT COMMENT '备注'
);
```

#### 2. 盘点任务表 (asset_stocktaking_task)
```sql
CREATE TABLE asset_stocktaking_task (
    task_id VARCHAR(32) PRIMARY KEY COMMENT '盘点任务ID',
    plan_id VARCHAR(32) NOT NULL COMMENT '盘点计划ID',
    task_name VARCHAR(100) COMMENT '任务名称',
    assigned_user_id BIGINT COMMENT '分配给的用户ID',
    asset_scope TEXT COMMENT '资产范围（JSON格式）',
    expected_count INT DEFAULT 0 COMMENT '预期盘点数量',
    actual_count INT DEFAULT 0 COMMENT '实际盘点数量',
    status TINYINT DEFAULT 1 COMMENT '状态：1-待执行，2-执行中，3-已完成',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间'
);
```

#### 3. 盘点记录表 (asset_stocktaking_record)
```sql
CREATE TABLE inventory_record (
    record_id VARCHAR(32) PRIMARY KEY COMMENT '盘点记录ID',
    task_id VARCHAR(32) NOT NULL COMMENT '盘点任务ID',
    asset_id VARCHAR(32) COMMENT '资产ID',
    asset_code VARCHAR(50) COMMENT '资产编码',
    found_status TINYINT COMMENT '发现状态：1-找到，0-未找到',
    actual_location VARCHAR(200) COMMENT '实际位置',
    actual_status TINYINT COMMENT '实际状态',
    inventory_user_id BIGINT COMMENT '盘点人ID',
    inventory_time DATETIME COMMENT '盘点时间',
    remark TEXT COMMENT '备注'
);
```

#### 4. 盘点差异表 (inventory_difference)
```sql
CREATE TABLE inventory_difference (
    diff_id VARCHAR(32) PRIMARY KEY COMMENT '差异ID',
    plan_id VARCHAR(32) NOT NULL COMMENT '盘点计划ID',
    asset_id VARCHAR(32) COMMENT '资产ID',
    diff_type TINYINT COMMENT '差异类型：1-盘盈，2-盘亏，3-状态差异，4-位置差异',
    book_value TEXT COMMENT '账面信息（JSON格式）',
    actual_value TEXT COMMENT '实际信息（JSON格式）',
    diff_reason VARCHAR(500) COMMENT '差异原因',
    handle_status TINYINT DEFAULT 1 COMMENT '处理状态：1-待处理，2-处理中，3-已处理',
    handle_suggestion TEXT COMMENT '处理建议',
    create_time DATETIME COMMENT '创建时间'
);
```

## 接口设计规范

### RESTful API 设计原则
- 使用标准HTTP方法（GET、POST、PUT、DELETE）
- 统一返回AjaxResult格式
- 遵循RuoYi框架的权限控制规范
- 所有接口需要添加操作日志

### 权限控制
- 盘点计划管理：`inventory:plan:*`
- 盘点任务执行：`inventory:task:*`
- 盘点结果查看：`inventory:result:*`
- 盘点报告导出：`inventory:report:*`

## 前端页面设计

### 页面结构
```
src/views/inventory/
├── plan/
│   ├── index.vue          # 盘点计划列表
│   ├── add.vue           # 新增盘点计划
│   └── detail.vue        # 盘点计划详情
├── task/
│   ├── index.vue          # 盘点任务列表
│   ├── execute.vue        # 执行盘点任务
│   └── progress.vue       # 盘点进度监控
├── result/
│   ├── index.vue          # 盘点结果查看
│   ├── difference.vue     # 差异分析
│   └── report.vue         # 盘点报告
└── components/
    ├── AssetScanner.vue   # 资产扫码组件
    └── ProgressChart.vue  # 进度图表组件
```

### UI设计要求
- 遵循Element UI设计规范
- 响应式布局，支持移动端访问
- 操作流程清晰，用户体验友好
- 重要操作需要确认提示

## 开发计划

### 第一阶段（2周）：基础功能开发
- 完成数据库表设计和创建
- 实现盘点计划管理功能
- 实现盘点任务分发功能
- 完成基础前端页面

### 第二阶段（2周）：核心功能开发
- 实现盘点任务执行功能
- 完成差异分析算法
- 实现盘点报告生成
- 前端功能完善和联调

### 第三阶段（1周）：集成测试和优化
- 与现有模块集成测试
- 性能优化和bug修复
- 用户体验优化
- 文档完善

## 质量要求

### 代码质量
- 代码注释覆盖率 > 80%
- 单元测试覆盖率 > 70%
- 遵循阿里巴巴Java开发规范
- 前端代码通过ESLint检查

### 性能要求
- 盘点计划查询响应时间 < 2秒
- 支持并发盘点任务数 > 50
- 大批量数据导出时间 < 30秒
- 前端页面加载时间 < 3秒

### 安全要求
- 所有接口需要权限验证
- 敏感操作需要二次确认
- 数据传输使用HTTPS
- 防止SQL注入和XSS攻击

## 验收标准

### 功能验收
- [ ] 所有功能点按需求实现
- [ ] 业务流程完整可用
- [ ] 与现有系统无缝集成
- [ ] 用户操作体验良好

### 技术验收
- [ ] 代码质量达标
- [ ] 性能指标满足要求
- [ ] 安全测试通过
- [ ] 兼容性测试通过

## 风险评估

### 技术风险
- **数据量大时的性能问题**：建议分批处理和异步执行
- **移动端兼容性**：需要充分测试不同设备和浏览器
- **并发盘点冲突**：需要设计合理的锁机制

### 业务风险
- **盘点流程复杂**：需要与业务人员充分沟通确认流程
- **权限控制复杂**：需要详细设计权限矩阵
- **数据一致性**：需要设计完善的事务控制

## 后续扩展

### 移动端支持
- 开发微信小程序版本
- 支持离线盘点功能
- 集成二维码/RFID扫描

### 智能化功能
- AI辅助差异分析
- 智能盘点路径规划
- 预测性盘点建议

---

**文档版本：** V1.0  
**创建日期：** 2025-01-14  
**最后更新：** 2025-01-14  
**文档状态：** 待评审
